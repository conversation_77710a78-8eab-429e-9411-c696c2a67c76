<div align="center">

# 🍽️ Wiz-Aroma Delivery Bot System

### *Intelligent Food Delivery Management Platform*

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Telegram Bot API](https://img.shields.io/badge/Telegram-Bot%20API-blue.svg)](https://core.telegram.org/bots/api)
[![Firebase](https://img.shields.io/badge/Firebase-Realtime%20DB-orange.svg)](https://firebase.google.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-2.0-brightgreen.svg)](https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-2.0)

*A comprehensive multi-bot Telegram-based food delivery system with advanced automation capabilities*

[🚀 Features](#-current-features) • [📋 Documentation](#-documentation) • [🔧 Installation](#-installation) • [🎯 Roadmap](#-development-roadmap)

</div>

---

## 📊 **System Status**

| Component | Status | Version | Performance |
|-----------|--------|---------|-------------|
| **User Bot** | ✅ Active | v2.0 | 99.5% Uptime |
| **Management Bot** | ✅ Active | v2.0 | Enhanced Analytics |
| **Delivery Bot** | ✅ Active | v2.0 | Real-time Assignment |
| **Order Tracking Bot** | ✅ Active | v2.0 | Real-time Updates |
| **Data Management** | ✅ Active | v2.0 | Seasonal Reset Ready |
| **Database** | ✅ Firebase | Realtime DB | 150-300ms Response |
| **Security** | 🔒 Secured | v2.0 | Enterprise-Grade Protection |

## 🎯 **Project Overview**

Wiz-Aroma is an advanced multi-bot Telegram delivery system currently serving **50-120 orders daily** with **enterprise-grade security** and **production-ready automation**. The system features a sophisticated architecture with specialized bots for different operational functions and comprehensive security hardening.

## 🚀 **Current Features**

### 👥 **Customer Experience**

- 🏪 **Multi-Restaurant Selection**: Browse restaurants by geographical area
- 📱 **Smart Menu System**: Intuitive categorized interface with real-time pricing
- 💫 **Points Reward System**: Earn 11% of delivery fee as loyalty points
- 💳 **Multiple Payment Methods**: Telebirr, CBE Bank, BOA Bank, Points redemption
- ⭐ **Favorite Orders**: One-click reordering of preferred meals
- 📍 **Delivery Tracking**: Basic order status updates
- ⏰ **Operating Hours**: Weekdays (5:30-7:30, 11:30-14:30), Weekends (5:30-14:30)

### 🤖 **Multi-Bot Architecture**

- **🛍️ User Bot** (`@wiz_aroma_bot`): Customer ordering interface
- **📊 Management Bot**: Comprehensive analytics, personnel management, and data operations
- **🚚 Delivery Bot**: Real-time order assignment and delivery coordination
- **🔍 Audit Bot**: System monitoring and performance tracking

### 🗄️ **Data Management System**

- **🔄 Seasonal Data Reset**: Complete system refresh while preserving critical data
- **🧹 Daily Order Cleanup**: Automated maintenance for optimal performance
- **📋 Audit Logging**: Comprehensive operation tracking and security
- **💾 Backup & Recovery**: Automated data archiving with 24-hour recovery window
- **🔐 Access Control**: Multi-layer authorization for sensitive operations
- **📊 Zero-Data Handling**: Graceful analytics display for fresh system states
- **👨‍💼 Admin Bot** (`@Wiz_aroma_admin_bot`): Order management and oversight
- **💰 Finance Bot** (`@Wiz_Aroma_Finance_bot`): Payment verification (currently manual)
- **🔧 Maintenance Bot** (`@Wiz_Aroma_Maintenance_bot`): System configuration
- **📢 Notification Bot**: Automated order notifications

### 🛠️ **Technical Features**

- **🔥 Firebase Integration**: Real-time database with local backup
- **🔒 Security**: Role-based access control and secure credential management
- **📊 Logging**: Comprehensive audit trails and error tracking
- **⚡ Performance**: 99.5% uptime with advanced monitoring and optimization
- **🔄 Data Sync**: Automatic synchronization between cloud and local storage

## � **Version 2.0 Achievements**

### 🎯 **Major Upgrades Completed**

#### 🔒 **Enterprise-Grade Security Implementation**

- **Achievement**: Zero hardcoded credentials in source code
- **Implementation**: Comprehensive environment variable system
- **Impact**: 100% GitHub-safe repository, production-ready security

#### �️ **Advanced Security Features**

- **Achievement**: Dynamic authorization system via Firebase Firestore
- **Implementation**: Real-time access control and audit logging
- **Impact**: Enhanced security monitoring and role-based permissions

#### 📊 **Data Management Automation**

- **Previous Version**: Manual tracking and calculation processes
- **Current**: Real-time analytics dashboard with automated reporting
- **Impact**: Automated data processing, comprehensive system insights

#### 🚀 **Performance Optimization**

- **Current**: Single-threaded operations, basic monitoring
- **Upgrade**: Caching layer, async processing, advanced monitoring
- **Impact**: 3x throughput increase, 99.9% uptime target

## 📱 **Quick Start Guide**

### 🛍️ **For Customers**

1. **Start Ordering**: Send `/start` to `@wiz_aroma_bot`
2. **Main Menu Options**:
   - 🍽️ **Order Food** - Browse and order from restaurants
   - 💫 **My Points** - Check loyalty points balance
   - ⭐ **My Favorites** - Quick reorder saved meals
   - ℹ️ **Help** - Get assistance and support

3. **Ordering Process**:

   ```
   Select Area → Choose Restaurant → Add Items →
   Delivery Details → Payment → Verification → Delivery
   ```

### 👨‍💼 **For Administrators**

- **Order Management**: Review and approve/reject incoming orders
- **Customer Communication**: Direct messaging with customers
- **System Monitoring**: Track order flow and system performance

## 🔧 **Installation & Setup**

### Prerequisites

```bash
Python 3.9+
Firebase Account
Telegram Bot Tokens
```

### Quick Installation

```bash
# Clone the repository
git clone https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-2.0.git
cd Wiz-Aroma-V-2.0

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env with your bot tokens and Firebase credentials

# Run the system
python main.py
```

## 📋 **Documentation**

### 🛠️ **Technical Documentation**

- **[SYSTEM_ARCHITECTURE.md](SYSTEM_ARCHITECTURE.md)** - System architecture overview
- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - Project structure details
- **[FIREBASE_SETUP.md](FIREBASE_SETUP.md)** - Firebase integration guide
- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Deployment instructions
- **[SECURITY.md](SECURITY.md)** - Security best practices

### 📊 **Configuration Files**

- **menu_and_restaurants.txt** - Restaurant and menu data
- **delivery_fees.txt** - Delivery locations and pricing
- **DATA_SYNC.md** - Data synchronization guide

## 📞 **Support & Contact**

### 🆘 **Get Help**

- **📧 Email**: [<EMAIL>](mailto:<EMAIL>)
- **💬 Telegram**: [@wiz_aroma_bot](https://t.me/wiz_aroma_bot)
- **🐛 Issues**: [GitHub Issues](https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-2.0/issues)

### 👨‍💻 **Developer**

**Mihretab Nigatu**

- GitHub: [@Mih-Nig-Afe](https://github.com/Mih-Nig-Afe)
- Email: <<EMAIL>>

## 📈 **Project Statistics**

| Metric | V1.3.3 | V2.0 (Current) | Target (V3.0) |
|--------|---------|----------------|---------------|
| **Daily Orders** | 30-80 | 50-120 | 200+ |
| **Processing Time** | 15-25 min | 10-15 min | <5 min |
| **System Uptime** | 99.2% | 99.5% | 99.9% |
| **Security Level** | Basic | Enterprise-Grade | Advanced AI |
| **Error Rate** | ~5% | ~2% | <1% |

## 🔒 **Security & Production Features**

### **🛡️ Enterprise-Grade Security**

- **🔐 Zero Hardcoded Credentials:** All sensitive data externalized to environment variables
- **🚫 GitHub-Safe Repository:** No production credentials exposed in source code
- **🔑 Dynamic Authorization:** Real-time access control via Firebase Firestore
- **📊 Comprehensive Audit Logging:** All admin actions tracked with timestamps
- **⚡ Rate Limiting:** Admin actions limited to prevent abuse (10/minute)
- **🚨 Error Reporting:** Critical errors automatically reported to administrators
- **✅ Graceful Error Handling:** Robust fallback mechanisms for all operations
- **🔄 Environment Validation:** System exits safely if required credentials missing

### **🎯 Access Control**

- **Admin Management:** Only authorized Telegram IDs can access administrative features
- **Role-Based Permissions:** Different access levels for different bot functions
- **Secure Fallbacks:** No hardcoded credentials in any fallback mechanisms
- **Production Ready:** Safe for public GitHub hosting and open-source distribution

## 🔧 **Environment Setup**

### **📋 Required Environment Variables**

Copy `.env.example` to `.env` and configure with your actual values:

```bash
# Bot Tokens - Get from @BotFather on Telegram
BOT_TOKEN=your_user_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token
FINANCE_BOT_TOKEN=your_finance_bot_token
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
MANAGEMENT_BOT_TOKEN=your_management_bot_token
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token
DELIVERY_BOT_TOKEN=your_delivery_bot_token

# Security - Authorized User IDs
SYSTEM_ADMIN_ID=your_primary_admin_telegram_id
ORDER_TRACK_BOT_AUTHORIZED_IDS="[your_admin_id_here]"
DELIVERY_BOT_AUTHORIZED_IDS="[your_admin_id_here]"
MANAGEMENT_BOT_AUTHORIZED_IDS="[your_admin_id_here, secondary_admin_id_here]"

# Contact Information
CUSTOMER_SERVICE_PHONE=your_customer_service_phone
CUSTOMER_SERVICE_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>

# Firebase Configuration
FIREBASE_CREDENTIALS_PATH=path/to/your/firebase-credentials.json
# OR use JSON credentials directly:
# FIREBASE_CREDENTIALS={"type":"service_account",...}
```

> **⚠️ Security Note:** Never commit your `.env` file to version control. It's already included in `.gitignore`.

## ✅ **Production Deployment Checklist**

### **🔒 Security Compliance**

- [x] **Zero Hardcoded Credentials:** All sensitive data moved to environment variables
- [x] **GitHub-Safe Repository:** No production credentials in source code
- [x] **Environment Validation:** System exits gracefully if credentials missing
- [x] **Secure Fallbacks:** No hardcoded values in any fallback mechanisms
- [x] **Access Control:** Restrict admin features to authorized Telegram IDs only

### **🛠️ System Readiness**

- [x] **Clean Codebase:** Remove all test/debug code and development artifacts
- [x] **Audit Logging:** Enable comprehensive logging for all admin actions
- [x] **Rate Limiting:** Implement protection against admin action abuse
- [x] **Error Handling:** Robust error reporting and user feedback systems
- [x] **Data Integrity:** Verify all Firestore data consistency and accuracy
- [x] **End-to-End Testing:** Complete system functionality verification

### **📋 Final Verification**

- [x] **Repository Structure:** Clean, organized, and professional
- [x] **Documentation:** Complete setup instructions and API documentation
- [x] **Security Audit:** Comprehensive credential elimination completed
- [x] **Performance Testing:** System ready for production load

## 📊 **Version History & Roadmap**

### **🎯 Current Version: 2.0**

- **Release Date:** August 1, 2025
- **Major Achievement:** Enterprise-Grade Security Implementation
- **Key Features:** Zero hardcoded credentials, GitHub-safe repository, production-ready security

### **🚀 Version Roadmap**

| Version | Status | Key Features | Target Date |
|---------|--------|--------------|-------------|
| **V1.3.3** | ✅ **Released** | Multi-bot system, Firebase integration | July 2025 |
| **V2.0** | ✅ **Current** | Enterprise security, credential elimination | August 2025 |
| **V3.0** | 🔄 **Planned** | AI automation, intelligent distribution | Q4 2025 |
| **V4.0** | 📋 **Future** | Mobile app, multi-language support | Q1 2026 |

---

<div align="center">

### 🌟 **Star this repository if you find it helpful!**

**Made with ❤️ by [Mihretab Nigatu](https://github.com/Mih-Nig-Afe)**

*Transforming food delivery through intelligent automation*

</div>
