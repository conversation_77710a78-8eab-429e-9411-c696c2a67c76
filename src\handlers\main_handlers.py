"""
Main handlers for the Wiz Aroma Delivery Bot.
Includes help and introduction handlers.
"""

from src.bot_instance import bot
from src.config import (
    logger,
    TELEBIRR_NAME,
    TELEBIRR_PHONE,
    CBE_ACCOUNT_NUMBER,
    CBE_ACCOUNT_NAME,
    BOA_ACCOUNT_NUMBER,
    BOA_ACCOUNT_NAME,
    SUPPORT_PHONE_1,
    SUPPORT_PHONE_2
)
from src.utils.keyboards import get_main_menu_markup, get_areas_markup
from src.data_models import (
    orders,
    order_status,
    awaiting_receipt,
    delivery_locations,
    current_order_numbers,
)
from src.utils.time_utils import is_open_now
from src.utils.error_handling import handle_exceptions
from src.utils.handler_registration import register_handler


@register_handler("user", commands=["start"])
@handle_exceptions(error_message="Error showing welcome message")
def start(message):
    """Handle the /start command"""
    markup = get_main_menu_markup()

    welcome_text = (
        "🌟 Welcome to Wiz Aroma Food Delivery!\n\n"
        f"Hey {message.from_user.first_name}! 👋\n\n"
        "🍽️ What We Offer:\n"
        "• Multiple restaurants in your area\n"
        "• Fast delivery to your gate\n"
        "• Easy payment options\n"
        "• Earn points on every delivery\n\n"
        "💫 Points System:\n"
        "• Earn (10 + 1)% of delivery fee as points\n"
        "• Use points to cover delivery fees\n"
        "• 1 point = 1 birr\n\n"
        "🚀 Ready to order? Click 'Order Food' to begin!"
    )

    # Check if the bot is open and add to the welcome message
    if is_open_now():
        welcome_text += "\n\n🕒 We are currently open for orders!"
    else:
        welcome_text += (
            "\n\n🚫 We are currently closed. Our working hours are:\n"
            "• Monday to Friday: 5:30 - 7:30 LT and 11:30 - 14:30 LT\n"
            "• Saturday and Sunday: 5:30 - 14:30 LT"
        )

    bot.send_message(message.chat.id, welcome_text, reply_markup=markup)
    logger.info(f"Welcome message sent to user {message.from_user.id}")


@handle_exceptions(error_message="Error showing help information")
def show_help(message):
    """Show help information to the user"""
    help_text = """
🌟 *Welcome to WizAroma Delivery Bot!*

*Operating Hours:*
• Mon-Fri: 5:30-7:30 & 11:30-14:30
• Sat-Sun: 5:30-14:30
_(Ethiopian Local Time)_

*Quick Order Guide:*
1️⃣ Click "🍽️ Order Food"
2️⃣ Select restaurant area
3️⃣ Choose restaurant & add items
4️⃣ Complete order & select delivery location
5️⃣ Provide contact details
6️⃣ Make payment & send receipt
7️⃣ Wait for delivery confirmation

*Payment Options:*
• 📱 Telebirr: {TELEBIRR_NAME}, {TELEBIRR_PHONE}
• 🏦 CBE: Account #{CBE_ACCOUNT_NUMBER}, {CBE_ACCOUNT_NAME}
• 🏦 BOA: {BOA_ACCOUNT_NAME}, {BOA_ACCOUNT_NUMBER}
• 💫 Points: Use for delivery fees

*Points System:*
• Earn (10 + 1)% of delivery fee as points
• Use points to cover delivery fees
• 1 point = 1 birr discount
• Check balance with "💫 My Points"

*Order Status Flow:*
1. Order Placed ➡️ Admin Review
2. Payment Required ➡️ Send Receipt
3. Payment Verification ➡️ Delivery

*Tips for Smooth Ordering:*
• Send payment screenshots as compressed photos
• Keep your phone available for delivery calls
• Use "🔙 Back to Main Menu" to start over
• Click "❌ Cancel Order" to cancel anytime

*Need Help?*
Contact: {SUPPORT_PHONE_1} & {SUPPORT_PHONE_2}

_Ready to order? Click 🍽️ Order Food below!_"""

    bot.send_message(
        message.chat.id,
        help_text,
        parse_mode="Markdown",
        reply_markup=get_main_menu_markup(),
    )
    logger.info(f"Help information sent to user {message.from_user.id}")


@handle_exceptions(error_message="Error showing restaurant areas")
def show_areas(message):
    """Show available areas for ordering"""
    user_id = message.from_user.id
    logger.info(f"User {user_id} is starting a new order")

    # Check if the bot is currently open for orders
    if not is_open_now():
        markup = get_main_menu_markup()

        bot.send_message(
            message.chat.id,
            "⏰ Sorry, we are currently closed for orders!\n\n"
            "Our working hours are:\n"
            "• Mon-Fri: 5:30-7:30 & 11:30-2:30\n"
            "• Sat-Sun: 5:30-2:30\n"
            "(Ethiopian Local Time)\n\n"
            "Please come back during our operating hours. Thank you!",
            reply_markup=markup,
        )
        logger.info(f"User {user_id} attempted to order outside operating hours")
        return

    # Clear any existing order data when starting a new order
    if user_id in orders:
        orders.pop(user_id)
    if user_id in awaiting_receipt:
        awaiting_receipt.pop(user_id)
    if user_id in order_status:
        order_status.pop(user_id)
    if user_id in delivery_locations:
        delivery_locations.pop(user_id)
    if user_id in current_order_numbers:
        current_order_numbers.pop(user_id)

    logger.debug(f"Cleared existing order data for user {user_id}")

    markup = get_areas_markup()

    bot.send_message(
        message.chat.id,
        "Please select your preferred area to see available restaurants:",
        reply_markup=markup,
    )
    logger.info(f"Showed restaurant areas to user {user_id}")


@register_handler("user", commands=["debug"])
@handle_exceptions(error_message="Error showing debug information")
def debug_status(message):
    """Debug command to check user's current order status"""
    user_id = message.from_user.id
    logger.info(f"User {user_id} requested debug information")

    # Get current status
    current_status = order_status.get(user_id, "No active order")

    # Get order details if available
    order_details = "No order details available"
    if user_id in orders:
        order = orders[user_id]
        order_details = f"Order number: {order.get('order_number', 'N/A')}\n"
        order_details += (
            f"Payment method: {order.get('payment_method', 'Not selected')}\n"
        )
        order_details += f"Subtotal: {order.get('subtotal', 0)} birr\n"
        order_details += f"Delivery fee: {order.get('delivery_fee', 0)} birr\n"
        order_details += f"Points used: {order.get('points_used', 0)}\n"

    # Check if awaiting receipt
    receipt_status = "Not awaiting receipt"
    if user_id in awaiting_receipt:
        receipt_info = awaiting_receipt[user_id]
        if isinstance(receipt_info, dict):
            receipt_status = (
                f"Awaiting receipt for {receipt_info.get('method', 'unknown')} payment"
            )
        else:
            receipt_status = f"Receipt status: {receipt_info}"

    debug_text = (
        "🔍 Debug Information:\n\n"
        f"User ID: {user_id}\n"
        f"Current status: {current_status}\n\n"
        f"Order details:\n{order_details}\n"
        f"Receipt status: {receipt_status}\n"
    )

    bot.send_message(message.chat.id, debug_text)
    logger.debug(f"Debug information sent to user {user_id}")

    # If in payment method selection, resend the payment options
    if current_status == "AWAITING_PAYMENT_METHOD":
        from src.utils.keyboards import get_payment_method_markup
        from src.data_models import user_points

        # Check if user has enough points for delivery fee
        user_point_balance = user_points.get(user_id, 0)
        delivery_fee = 0
        if user_id in orders:
            delivery_fee = orders[user_id].get("delivery_fee", 0)

        markup = get_payment_method_markup(user_point_balance >= delivery_fee)

        bot.send_message(
            message.chat.id,
            "Please select your payment method:",
            reply_markup=markup,
        )
        logger.debug(f"Resent payment options to user {user_id}")


@register_handler("user", commands=["help"])
@handle_exceptions(error_message="Error showing help information")
def help_command(message):
    """Handle the /help command"""
    # Reuse the existing show_help function
    show_help(message)


@register_handler("user", commands=["order"])
@handle_exceptions(error_message="Error starting food order")
def order_command(message):
    """Handle the /order command"""
    # Create a message object with the text "🍽️ Order Food" to reuse the existing handler
    message.text = "🍽️ Order Food"
    # Call the existing show_areas function
    show_areas(message)


@register_handler("user", commands=["points"])
@handle_exceptions(error_message="Error showing points balance")
def points_command(message):
    """Handle the /points command"""
    # Import the show_points_balance function from user_profile_handlers
    from src.handlers.user_profile_handlers import show_points_balance

    # Create a message object with the text "💫 My Points" to reuse the existing handler
    message.text = "💫 My Points"
    # Call the existing show_points_balance function
    show_points_balance(message)


@register_handler("user", commands=["favorite"])
@handle_exceptions(error_message="Error showing favorite orders")
def favorite_command(message):
    """Handle the /favorite command"""
    # Import the existing favorite orders handler
    from src.handlers.favorite_orders_handlers import show_favorite_orders

    # Reuse the message text trigger for favorite orders
    message.text = "⭐ Favorite Orders"
    show_favorite_orders(message)


# Register message handlers with the old decorator approach for backward compatibility
# These will be replaced by the centralized registration system
# These functions are called by the telebot framework, so they're not unused despite IDE warnings
@bot.message_handler(commands=["start"])
def _start_compat(message):  # noqa
    start(message)


@bot.message_handler(func=lambda message: message.text == "ℹ️ Help")
def _help_compat(message):  # noqa
    show_help(message)


@bot.message_handler(
    func=lambda message: message.text and "🍽️ Order Food" in message.text
)
def _order_food_compat(message):  # noqa
    show_areas(message)


@bot.message_handler(func=lambda message: message.text == "🔙 Back to Main Menu")
def _back_to_main_menu_compat(message):  # noqa
    back_to_main_menu(message)


@handle_exceptions(error_message="Error returning to main menu")
def back_to_main_menu(message):
    """Handle returning to the main menu from any point in the bot conversation"""
    user_id = message.from_user.id

    # Clear any existing order data
    if user_id in orders:
        orders.pop(user_id)
    if user_id in awaiting_receipt:
        awaiting_receipt.pop(user_id)
    if user_id in order_status:
        order_status.pop(user_id)
    if user_id in delivery_locations:
        delivery_locations.pop(user_id)
    if user_id in current_order_numbers:
        current_order_numbers.pop(user_id)

    logger.debug(f"Cleared order data for user {user_id} returning to main menu")

    # Create main menu markup and send welcome message
    markup = get_main_menu_markup()

    bot.send_message(
        message.chat.id,
        "You've returned to the main menu. What would you like to do next?",
        reply_markup=markup,
    )

    logger.info(f"User {user_id} returned to main menu")


# Add another handler for the version without emoji
@bot.message_handler(func=lambda message: message.text == "Back to Main Menu")
def _back_to_main_menu_no_emoji_compat(message):  # noqa
    back_to_main_menu(message)
